from enum import Enum, unique

from quick_calc_rec_service.model import util
from asteval import Interpreter
import math
from fractions import Fraction

aeval = Interpreter()
import re
from abc import ABC, abstractmethod


# 快速计算
def quick_correcting(pred_str):
    if pred_str.find("P") != -1:
        return False
    # 部分情况不走快速计算
    flag = util.quick_correcting_check_expression(pred_str)
    if flag is not True:
        return flag
    pred_str = pred_str.replace("$", "")
    pred_str = pred_str.replace("^2", "W").replace("^3", "E")  # 替换平方和立方

    #     F{}{} 转换成{/}的样式; ((2/3) / (1/2)) 的情况会出现异常
    pred_str = pred_str.replace("F{", "((").replace("}{", ")/(").replace("}", "))")
    if (
        pred_str.find("=") == -1
        and pred_str.find(">") == -1
        and pred_str.find("<") == -1
    ):
        return False
    pred_str = util.quick_change_unit(pred_str)  # 将单位换算成*100等形式
    pred_str = pred_str.replace("$", "")
    if pred_str.find("=") != -1:
        pred_str = pred_str.replace("==", "=")  # 避免识别问题
        item_lists = pred_str.split("=")
        if len(item_lists) > 2:
            return False
        try:
            if abs(aeval.eval(item_lists[0]) - aeval.eval(item_lists[1])) < 1e-6:
                return True
            else:
                return False
        except Exception as e:
            return False

    if pred_str.find("<") != -1:
        item_lists = pred_str.split("<")
        if len(item_lists) > 2:
            return False
        try:
            if aeval.eval(item_lists[0]) - aeval.eval(item_lists[1]) < -1e-6:
                return True
            else:
                return False
        except:
            return False

    if pred_str.find(">") != -1:
        item_lists = pred_str.split(">")

        if len(item_lists) > 2:
            return False
        try:
            if aeval.eval(item_lists[0]) - aeval.eval(item_lists[1]) > 1e-6:
                return True
            else:
                return False
        except:
            return False

    return False


# 处理 2米+3米=5 单位不完整这种情况
def wrong_and_no_answer_type_type1(final, pred_str, stem, answer_list):
    # 将单位列表拼接成一个正则表达式字符串
    unit_pattern = "(" + "|".join(re.escape(key) for key in util.units.keys()) + ")"
    bring_unit_formula = (
        unit_pattern.split("一")[0][:-1] + ")"
    )  # 当发现单位换算的题目，等号两边不是都有单位的时候则判错，不推荐答案。
    # 当发现题目有等号，且包含单位时，根据等号进行切割
    if pred_str.find("=") != -1 and re.search(bring_unit_formula, pred_str):
        items = pred_str.split("=")
        for item in items:
            # 如果 item 不包含 bring_unit_formula 的单位则返回
            if not re.search(bring_unit_formula, item):
                return "1"


# 处理 数字(>,<)数字(<,>)数字这种情况
def wrong_and_no_answer_type_type2(final, pred_str, stem, answer_list):
    stem, answer_list = util.preprocess_stem(stem, answer_list)
    try:
        pred_str = pred_str.replace("$", "")
        # 定义正则表达式匹配数字不等式
        # 用于匹配由一个或多个可选括号包裹的数字组成的链式不等式
        pattern = re.compile(r"(\(?\d+(\.\d+)?\)?(?:[<>]\(?\d+(\.\d+)?\)?)+)")
        # 检查是否匹配数字不等式
        match = pattern.fullmatch(pred_str)
        if not match:
            return None
        pred_str = pred_str.replace("$", "")
        # 定义正则表达式
        pattern = re.compile(r"(\(?\d+(\.\d+)?\)?|<|>)")
        # 提取所有数字和符号
        parts = pattern.findall(pred_str)
        parts = [part[0].replace("(", "").replace(")", "") for part in parts]
        # 检查是否匹配到正确的模式
        # ['345', '<', '456', '<', '480']所以len(parts)至少要五个
        if len(parts) < 4 or len(parts) % 2 == 0:
            return None
        # 解析第一个数字
        current_value = float(parts[0])
        # 遍历运算符和后续数字
        for i in range(1, len(parts), 2):
            operator = parts[i]
            next_value = float(parts[i + 1])
            if operator not in ["<", ">"]:
                return None
            if operator == ">" and not current_value > next_value:
                return False
            elif operator == "<" and not current_value < next_value:
                return False

            current_value = next_value
        return True
    except:
        return None


# 处理 数字+数字+数字=数字×数字=数字×数字=数字这种题型
def wrong_and_no_answer_type_type3(final, pred_str, stem, answer_list):
    stem, answer_list = util.preprocess_stem(stem, answer_list)
    pred_str = pred_str.replace("$", "")
    space = util.find_space(stem)
    # 最少有4个空,才判断是连等式子
    if space < 4:
        return None
    # 定义匹配数字+数字+数字=数字×数字=数字×数字=数字 的正则表达式
    pattern = re.compile(
        r"([0-9\+\-\*/\(\)]+)(=[0-9\(\)]+[*][0-9\(\)]+)(=[0-9\(\)]+[*][0-9\(\)]+)(=[0-9\(\)]+)"
    )

    match = pattern.match(pred_str)
    try:
        if match:
            # 提取等号分割的表达式部分
            parts = pred_str.split("=")

            # 对每个部分进行计算
            def calculate_expression(expression):
                # 去掉可能的括号
                try:
                    return aeval.eval(expression)
                except Exception as e:
                    return None

            # 计算前面不确定数量的加减乘除部分
            sum_part = calculate_expression(parts[0])

            # 计算后面的确定部分5
            product1 = calculate_expression(parts[1])
            product2 = calculate_expression(parts[2])
            result = calculate_expression(parts[3])
            if (
                sum_part is None
                or product1 is None
                or product2 is None
                or result is None
            ):
                return None
            # 判断等式
            if sum_part == product1 and product1 == product2 and product2 == result:
                return True
            else:
                return False
        else:
            return None
    except:
        return None


# 特殊题型 只判错并且不推荐答案的题型
def wrong_and_no_answer_question(final, pred_str, stem, answer_list):
    # 处理 2米+3米=5 单位不完整这种情况
    flag = wrong_and_no_answer_type_type1(final, pred_str, stem, answer_list)
    if flag is not None:
        if final["flag"] == flag:
            return final

    # 处理 数字(>,<)数字(<,>)数字这种情况
    result = wrong_and_no_answer_type_type2(final, pred_str, stem, answer_list)
    if result is not None:
        if result:
            final["flag"] = "0"
        else:
            final["flag"] = "1"
            final["reg_answers"] = answer_list
            final["std_answers"] = ["NONE"]
        return final

    # 处理 数字+数字+数字=数字×数字=数字×数字=数字这种题型
    result = wrong_and_no_answer_type_type3(final, pred_str, stem, answer_list)
    if result is not None:
        if result:
            final["flag"] = "0"
        else:
            final["flag"] = "1"
            final["reg_answers"] = answer_list
            final["std_answers"] = ["NONE"]
        return final


@unique
class SQT(Enum):
    sqt_1 = "sqt_1"
    sqt_2 = "sqt_2"
    sqt_3 = "sqt_3"
    sqt_4 = "sqt_4"
    sqt_5 = "sqt_5"
    sqt_6 = "sqt_6"
    sqt_7 = "sqt_7"
    sqt_8 = "sqt_8"
    sqt_9 = "sqt_9"
    sqt_10 = "sqt_10"
    sqt_11 = "sqt_11"
    sqt_12 = "sqt_12"
    sqt_13 = "sqt_13"
    sqt_14 = "sqt_14"
    sqt_15 = "sqt_15"
    sqt_16 = "sqt_16"
    sqt_17 = "sqt_17"
    sqt_18 = "sqt_18"
    sqt_19 = "sqt_19"
    sqt_20 = "sqt_20"
    sqt_21 = "sqt_21"
    sqt_22 = "sqt_22"
    sqt_23 = "sqt_23"
    sqt_24 = "sqt_24"
    sqt_25 = "sqt_25"
    sqt_26 = "sqt_26"
    sqt_27 = "sqt_27"
    sqt_28 = "sqt_28"
    sqt_29 = "sqt_29"


class SpecialQuestionStrategy(ABC):
    @abstractmethod
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        pass

    @abstractmethod
    def check(self, pred_str, stem, answer_list):
        pass


# F{12}{18}=2:3
class SQT1Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        # 定义正则表达式来匹配 F{12}{18}=2:3 这种格式
        pattern = re.compile(r"F\{(\d+)\}\{(\d+)\}=\$?\(?(\d+):(\d+)\)?\$?")
        match = pattern.fullmatch(pred_str)
        return match

    def handle(self, ori_line, final, pred_str, stem, answer_list):
        # 定义正则表达式来匹配 F{12}{18}=2:3 这种格式
        pattern = re.compile(r"F\{(\d+)\}\{(\d+)\}=\$?\(?(\d+):(\d+)\)?\$?")

        match = pattern.fullmatch(pred_str)

        if match:
            # 提取分子和分母
            num1 = int(match.group(1))
            num2 = int(match.group(2))
            # 提取比值
            num3 = int(match.group(3))
            num4 = int(match.group(4))
            gcd = math.gcd(num1, num2)
            # 算出标准答案
            std_ans = f"{num1 // gcd}:{num2 // gcd}"
            try:
                if (
                    abs(aeval.eval(f"{num1}/{num2}") - aeval.eval(f"{num3}/{num4}"))
                    < 1e-6
                ):
                    final["flag"] = "0"
                    return final
                else:
                    final["flag"] = "1"
                    final["reg_answers"] = answer_list
                    final["std_answers"] = [std_ans]
                    return final
            except:
                return None


# 3+5*(3-1)=B3, 100/4=B5      左边任意的复杂表达式，右边B数字
class SQT2Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        # 定义正则表达式以匹配等号左边的任意复杂的表达式
        left_pattern = re.compile(r"[\d+\-*/()]+")
        # 定义正则表达式以匹配等号右边的数字B数字格式
        right_pattern = re.compile(r"B(\d+)")
        # 分割字符串为等号左边和右边的部分
        parts = stem.split("=")
        if len(parts) == 2:
            left_part = parts[0]
            right_part = parts[1]

            # 检查左边部分是否匹配表达式，右边部分是否匹配数字B数字格式
            if (
                left_pattern.match(left_part)
                and right_pattern.match(right_part)
                and len(answer_list) == 1
            ):
                left_value = util.find_best_round(aeval.eval(left_part), 2)
                # 提取右边表达式中的数字部分
                match = right_pattern.match(right_part)
                return match

    def handle(self, ori_line, final, pred_str, stem, answer_list):
        # 定义正则表达式以匹配等号左边的任意复杂的表达式
        left_pattern = re.compile(r"[\d+\-*/()]+")
        # 定义正则表达式以匹配等号右边的数字B数字格式
        right_pattern = re.compile(r"B(\d+)")
        # 分割字符串为等号左边和右边的部分
        parts = stem.split("=")
        if len(parts) == 2:
            left_part = parts[0]
            right_part = parts[1]

            # 检查左边部分是否匹配表达式，右边部分是否匹配数字B数字格式
            if (
                left_pattern.match(left_part)
                and right_pattern.match(right_part)
                and len(answer_list) == 1
            ):
                left_value = util.find_best_round(aeval.eval(left_part), 2)
                # 提取右边表达式中的数字部分
                match = right_pattern.match(right_part)
                if match:
                    num_after_B = int(match.group(1))

                    # 计算B的值，考虑结果的长度
                    left_length = len(str(left_value))
                    right_length = len(str(num_after_B))
                    B_value_length = left_length - right_length

                    # 计算B的值
                    std_ans = left_value // (10**B_value_length)  # 得到B的值
                    first_judge = util.judge_answer_blur(answer_list[0], std_ans)
                    if first_judge:
                        final["flag"] = "0"
                        return final
                    else:
                        final["flag"] = "1"
                        final["reg_answers"] = [answer_list[0]]
                        final["std_answers"] = [std_ans]
                        return final


# 带单位的加减法题型
class SQT3Strategy(SpecialQuestionStrategy):
    def pattern1(self, kk):
        return re.compile("[0-9.]+{}[\+\-\*/][0-9.]+{}=[(B)]?".format(kk, kk))

    def pattern2(self, kk):
        return re.compile("([0-9.]+{}[\+\-\*/])+[0-9.]+{}=[(B)]?".format(kk, kk))

    def check(self, pred_str, stem, answer_list):
        for kk in util.units.keys():
            match1 = self.pattern1(kk).match(stem)
            match2 = self.pattern2(kk).match(stem)
            if (match1 or match2) and len(answer_list) == 1:
                return True

    def handle(self, ori_line, final, pred_str, stem, answer_list):
        for kk in util.units.keys():
            match1 = self.pattern1(kk).match(stem)
            match2 = self.pattern2(kk).match(stem)
            if (match1 or match2) and len(answer_list) == 1:
                ans_num = util.find_best_round(
                    aeval.eval(stem.split("=")[0].replace(kk, ""))
                )
                user_ans = stem.split("=")[1]
                # if kk not in user_ans:
                if kk != user_ans.replace("(", "").replace(")", "").replace("B", ""):
                    # 如果用户作答的单位是跨单位的情况
                    last_unit = util.find_unit_in_string(stem)
                    if last_unit is not None:
                        if kk in ["半小时", "小时", "时", "秒"] and last_unit == "分":
                            point_value = "*60"
                        else:
                            point_value = util.units[last_unit]

                        if last_unit in ["半小时", "小时", "时", "秒"] and kk == "分":
                            kk_value = "*60"
                        else:
                            kk_value = util.units[kk]
                        ans_str = util.find_best_round(
                            aeval.eval(
                                f"{ans_num}{kk_value}/{point_value.replace('*', '')}"
                            ),
                            2,
                        )
                    else:
                        ans_str = f"{ans_num}{kk}"
                else:
                    ans_str = ans_num

                dist_gap = aeval.eval(
                    stem.split("=")[0].replace(kk, "")
                    + "-"
                    + answer_list[0].replace(kk, "")
                )

                if (answer_list[0].find(kk) != -1 or kk in stem) and abs(
                    dist_gap
                ) < 1e-6:
                    final["flag"] = "0"
                    return final
                else:
                    final["flag"] = "1"
                    final["reg_answers"] = [answer_list[0]]
                    if "%" in stem:
                        if stem.find("+") != -1 or stem.find("-") != -1:
                            if answer_list[0].find("%") != -1:
                                ans_str = f"{ans_num}%"
                            else:
                                ans_num = ans_num / 100
                                if ans_num.is_integer():
                                    ans_str = str(int(ans_num))
                                else:
                                    ans_str = str(ans_num)
                        elif stem.find("*") != -1:
                            if answer_list[0].find("%") != -1:
                                ans_num = ans_num / 100
                                if ans_num.is_integer():
                                    ans_str = f"{int(ans_num)}%"
                                else:
                                    ans_str = f"{ans_num}%"
                            else:
                                ans_str = str(round(ans_num / 10000, 5))
                        else:
                            ans_str = str(ans_num)
                    final["std_answers"] = [ans_str]
                    if util.judge_answer_blur(answer_list[0], ans_str):
                        final = {"flag": "0", "reg_answers": [], "std_answers": []}
                        return final
                    return final


# 4702300092≈(B)亿  题型
class SQT4Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        pattern = re.compile(r"(\d+)≈\(?\$(\d+(\.\d+)?[万亿])\$\)?")
        match = pattern.match(pred_str)
        if not match is None and len(answer_list) == 1:
            return match

    # 4702300092≈(B)亿  题型
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        pattern = re.compile(r"(\d+)≈\(?\$(\d+(\.\d+)?[万亿])\$\)?")
        m = pattern.match(pred_str)
        if not m is None and len(answer_list) == 1:
            if "万" in answer_list[0]:
                unit_times = 10000
            if "亿" in answer_list[0]:
                unit_times = 100000000

            if "." not in answer_list[0]:
                decimal_len = 0
            else:
                decimal_len = len(answer_list[0][:-1]) - answer_list[0].find(".") - 1

            std_ans = round(float(stem.split("≈")[0]) / unit_times, decimal_len)
            std_ans = (
                str(util.find_best_round(std_ans, 2))
                if decimal_len != 0
                else str(int(std_ans))
            )
            first_judge = util.judge_answer_blur(std_ans, answer_list[0][:-1])
            if first_judge:
                final["flag"] = "0"
                return final
            else:
                final["flag"] = "1"
                final["reg_answers"] = [answer_list[0]]
                if "万" in answer_list[0] or "亿" in answer_list[0]:
                    std_ans = f"{std_ans}{answer_list[0][-1]}"
                final["std_answers"] = [std_ans]
                return final


# 4702300092=(B)亿  题型
class SQT5Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        pattern = re.compile(r"(\d+)=\(?\$(\d+(\.\d+)?[万亿])\$\)?")
        match = pattern.match(pred_str)
        if not match is None and len(answer_list) == 1:
            return match

    def handle(self, ori_line, final, pred_str, stem, answer_list):
        pattern = re.compile(r"(\d+)=\(?\$(\d+(\.\d+)?[万亿])\$\)?")
        m = pattern.match(pred_str)
        if not m is None and len(answer_list) == 1:
            if "万" in answer_list[0]:
                unit_times = 10000
            if "亿" in answer_list[0]:
                unit_times = 100000000

            std_ans = float(stem.split("=")[0]) / unit_times
            first_judge = util.judge_answer_blur(std_ans, answer_list[0][:-1])
            if first_judge:
                final["flag"] = "0"
                return final
            else:
                final["flag"] = "1"
                final["reg_answers"] = [answer_list[0]]
                if "万" in answer_list[0] or "亿" in answer_list[0]:
                    std_ans = f"{util.find_best_round(std_ans)}{answer_list[0][-1]}"
                final["std_answers"] = [std_ans]
                return final


# 1$+$2$-$2=1题型
class SQT6Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        # 1$+$2$-$2=1题型
        pattern = re.compile("[0-9.]+B[0-9.]+B[0-9.]+=[0-9.]+")
        match = pattern.match(stem)
        if match is not None:
            return match

    def handle(self, ori_line, final, pred_str, stem, answer_list):
        # 1$+$2$-$2=1题型
        pattern = re.compile("[0-9.]+B[0-9.]+B[0-9.]+=[0-9.]+")
        m = pattern.match(stem)
        if m is not None:
            top, bottom = stem.split("=")
            bodies = top.split("B")
            for first in ["+", "-", "*", "/"]:
                for second in ["+", "-", "*", "/"]:
                    if (
                        abs(
                            aeval.eval(
                                bodies[0] + first + bodies[1] + second + bodies[2]
                            )
                            - aeval.eval(bottom)
                        )
                        < 1e-6
                    ):
                        if answer_list[0] == first and answer_list[1] == second:
                            final["flag"] = "0"
                            return final
                        else:
                            final["flag"] = "1"
                            final["reg_answers"] = [answer_list[0], answer_list[1]]
                            final["std_answers"] = [first, second]
                            return final


# 处理1个数的约等于的情况
class SQT7Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list = util.preprocess_stem(stem, answer_list)
        pattern = re.compile(r"^\d+(\.\d+)?@(B|\(B\))$")
        match = pattern.match(stem)
        if not match is None and len(answer_list) == 1:
            return match

    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list = util.preprocess_stem(stem, answer_list)
        pattern = re.compile(r"^\d+(\.\d+)?@(B|\(B\))$")
        m = pattern.match(stem)
        if not m is None and len(answer_list) == 1:
            approx_results = []
            left, right = stem.split("@")
            if "." not in left:  # 如果题干是整数的情况
                integer_value = int(left)
                factor = 10
                if integer_value < 5:
                    approx_results.append(["0"])
                elif 5 <= integer_value <= 10:
                    approx_results.append(["10"])
                else:
                    while factor <= integer_value:
                        approx_value = round(integer_value / factor) * factor
                        if [str(approx_value)] not in approx_results:
                            approx_results.append([str(approx_value)])
                        factor *= 10
            else:  # 题干是有小数的情况
                right_length = util.handle_decimal(left)  # 计算小数点后有几位
                if right_length == 1:
                    approx_results.append(
                        [str(util.traditional_round(float(left)))]
                    )  # 取整数
                else:
                    approx_results.append(
                        [str(util.traditional_round(float(left)))]
                    )  # 取整数
                    for i in range(1, right_length):  # 取小数点后第1位开始
                        if [str(round(float(left), i))] not in approx_results:
                            approx_results.append([str(round(float(left), i))])

            # 判断正误
            if any(answer_list[0] == result[0] for result in approx_results):
                final["flag"] = "0"
            else:
                final["flag"] = "1"
                final["reg_answers"] = [answer_list[0]]
                final["std_answers"] = approx_results
            return final


# 枚举估算
class SQT8Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list = util.preprocess_stem(stem, answer_list)
        if re.match("[\d]+[\+\-\*/]{1}[\d]+@(B|\(B\))", stem):
            return True
        elif re.match("[\d.]+[\+\-\*/]{1}[\d.]+@(B|\(B\))", stem):
            return True
        else:
            return False

    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list = util.preprocess_stem(stem, answer_list)
        std_ans, formulas = util.gusuan(stem)
        if answer_list[0].endswith(".0"):
            answer_list[0] = answer_list[0][:-2]
        if not std_ans is None:
            # 判断正误
            if any(answer_list[0] == result for result in std_ans):
                final["flag"] = "0"
            else:
                final["flag"] = "1"
                final["reg_answers"] = [answer_list[0]]
                final["std_answers"] = formulas if formulas is not None else std_ans
                if len(final["std_answers"]) == 0:
                    final["std_answers"] = ["NONE"]
            return final


# 比大小题型
class SQT9Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list = util.preprocess_stem(stem, answer_list)
        # 处理只是比较大小的题型
        compare_line = util.quick_change_unit(stem)
        compare_line = compare_line.replace("space", "B")
        if len(answer_list) == 1 and answer_list[0] in [">", "<", "="]:
            pieces = compare_line.split("B")
            if len(pieces) == 2 and len(pieces[0]) > 0 and len(pieces[1]) > 0:
                return True

    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list = util.preprocess_stem(stem, answer_list)
        stem = util.replace_ge(stem)  # 7个十$>$8个一
        # 处理只是比较大小的题型
        compare_line = util.quick_change_unit(stem)
        compare_line = compare_line.replace("space", "B")
        try:
            if len(answer_list) == 1 and answer_list[0] in [">", "<", "="]:
                pieces = compare_line.split("B")
                if len(pieces) == 2 and len(pieces[0]) > 0 and len(pieces[1]) > 0:
                    left = aeval.eval(pieces[0])
                    right = aeval.eval(pieces[1])
                    if answer_list[0] == ">":
                        if left - right > 1e-9:
                            final["flag"] = "0"
                        elif abs(left - right) < 1e-9:
                            final["flag"] = "1"
                            final["reg_answers"] = [">"]
                            final["std_answers"] = ["="]
                        else:
                            final["flag"] = "1"
                            final["reg_answers"] = [">"]
                            final["std_answers"] = ["<"]
                    if answer_list[0] == "<":
                        if left - right < -1 * 1e-9:
                            final["flag"] = "0"
                        elif abs(left - right) < 1e-9:
                            final["flag"] = "1"
                            final["reg_answers"] = ["<"]
                            final["std_answers"] = ["="]
                        else:
                            final["flag"] = "1"
                            final["reg_answers"] = ["<"]
                            final["std_answers"] = [">"]
                    if answer_list[0] == "=":
                        if left - right < -1 * 1e-9:
                            final["flag"] = "1"
                            final["reg_answers"] = ["="]
                            final["std_answers"] = ["<"]
                        elif left - right > 1e-9:
                            final["flag"] = "1"
                            final["reg_answers"] = ["="]
                            final["std_answers"] = [">"]
                        else:
                            final["flag"] = "0"
                    return final
        except:
            return None


# 小数转分数，只支持‘1.25=’格式
class SQT10Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        stem = stem.replace("B", "").replace("C", "").replace("()", "")
        if re.match("\d+\\.\d+=", stem):
            inds = re.match("\d+.\d+=", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                return True

    # 小数转分数，只支持‘1.25=’格式
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        stem = stem.replace("B", "").replace("C", "").replace("()", "")
        if re.match("\d+\\.\d+=", stem):
            inds = re.match("\d+.\d+=", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                answer = util.change_float_frac(aeval.eval(stem[:-1]))
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
        return None


# 小数转分数，只支持'0.05=((B)/(B))'格式
class SQT11Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        # 匹配小数和分数形式的模式
        # 移除多余的字符
        stem = stem.replace("B", "").replace("C", "").replace("()", "")
        pattern = re.compile(r"(\d+\.\d+)=\(\/\)$")
        match = pattern.search(stem)
        return match

    # 小数转分数，只支持'0.05=((B)/(B))'格式
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        # 匹配小数和分数形式的模式
        # 移除多余的字符
        stem = stem.replace("B", "").replace("C", "").replace("()", "")
        pattern = re.compile(r"(\d+\.\d+)=\(\/\)$")
        match = pattern.search(stem)

        if match:
            # 获取小数部分
            decimal = match.group(1)

            # 将小数转换为分数
            fraction = Fraction(decimal).limit_denominator()
            fenzhi = fraction.numerator
            fenmu = fraction.denominator
            answer = fenzhi, fenmu
            return util.handle_answer_return(
                answer, ori_line, pred_str, stem, answer_list
            )
        return None


# 分数转小数，只支持‘{1/2}=’格式
class SQT12Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        stem = stem.replace("B", "").replace("C", "").replace("()", "")
        stem = stem.replace("{", "").replace("}", "")
        if re.match(r"\d+(\.\d+)?/\d+(\.\d+)?=", stem):
            inds = re.match(r"\d+(\.\d+)?/\d+(\.\d+)?=", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                return True

    # 分数转小数，只支持‘{1/2}=’格式
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        stem = stem.replace("B", "").replace("C", "").replace("()", "")
        stem = stem.replace("{", "").replace("}", "")
        if re.match(r"\d+(\.\d+)?/\d+(\.\d+)?=", stem):
            inds = re.match(r"\d+(\.\d+)?/\d+(\.\d+)?=", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                answer = aeval.eval(stem[:-1])
                pattern = re.compile(r"\d+(\.\d+)?:\d+(\.\d+)?=")
                if pattern.match(ori_line):
                    answer = util.change_float_proportion_list(float(answer))
                # 使用正则表达式匹配数字:数字=$数字:数字$的格式
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
        return None


# 处理 78/29 = F{($78$)}{($29$)}的情况
class SQT13Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        # 12/71=((B)/(B)) or 1.2/7.1=((B)/(B))
        if re.match("[\d.]+/[\d.]+=\(\(B\)/\(B\)\)", stem):
            return True
        # 12/71=(B/B)
        elif re.match("[\d.]+/[\d.]+=\(B/B\)", stem):
            return True
        # B/71=((12)/(B))
        elif re.match("B/[\d.]+=\(\([\d.]+\)/\(B\)\)", stem):
            return True
        # 12/B=((B)/(73))
        elif re.match("[\d.]+/B=\(\(B\)/\([\d.]+\)\)", stem):
            return True
        # 12/B=(B/(73))
        elif re.match("[\d.]+/B=\(B/\([\d.]+\)\)", stem):
            return True
        # B/B=((12)/(73))
        elif re.match("B/B=\(\([\d.]+\)/\([\d.]+\)\)", stem):
            return True
        # ((1.2)/(73))=B/B
        elif re.match("\(\([\d.]+\)/\([\d.]+\)\)=B/B", stem):
            return True
        # (B/B)=12/7.3
        elif re.match("\(B/B\)=[\d.]+/[\d.]+", stem):
            return True
        # ((B)/(B))=12/7.3
        elif re.match("\(\(B\)/\(B\)\)=[\d.]+/[\d.]+", stem):
            return True
        # ((B)/(73))=12/B
        elif re.match("\(\(B\)/\([\d.]+\)\)=[\d.]+/B", stem):
            return True
        # (B/(73))=12/B
        elif re.match("\(B/\([\d.]+\)\)=[\d.]+/B", stem):
            return True
        # ((12)/B)=B/7.3
        elif re.match("\(\([\d.]+\)/B\)=B/[\d.]+", stem):
            return True
        # ((12)/(B))=B/7.3
        elif re.match("\(\([\d.]+\)/\(B\)\)=B/[\d.]+", stem):
            return True
        else:
            return None

    # 处理 78/29 = F{($78$)}{($29$)}的情况
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        # 12/71=((B)/(B)) or 1.2/7.1=((B)/(B))
        if re.match("[\d.]+/[\d.]+=\(\(B\)/\(B\)\)", stem):
            inds = re.match("[\d.]+/[\d.]+=\(\(B\)/\(B\)\)", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split("/")[0]
                item2 = (stem.split("=")[0]).split("/")[1]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
        # 12/71=(B/B)
        if re.match("[\d.]+/[\d.]+=\(B/B\)", stem):
            inds = re.match("[\d.]+/[\d.]+=\(B/B\)", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split("/")[0]
                item2 = (stem.split("=")[0]).split("/")[1]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
        # B/71=((12)/(B))
        if re.match("B/[\d.]+=\(\([\d.]+\)/\(B\)\)", stem):
            inds = re.match("B/[\d.]+=\(\([\d.]+\)/\(B\)\)", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split("/")[1].split("=")[0]
                item2 = (stem.split("=((")[1]).split(")/(")[0]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )

        # 12/B=((B)/(73))
        if re.match("[\d.]+/B=\(\(B\)/\([\d.]+\)\)", stem):
            inds = re.match("[\d.]+/B=\(\(B\)/\([\d.]+\)\)", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split("/")[0]
                item2 = (stem.split(")/(")[1]).split("))")[0]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )

        # 12/B=(B/(73))
        if re.match("[\d.]+/B=\(B/\([\d.]+\)\)", stem):
            inds = re.match("[\d.]+/B=\(B/\([\d.]+\)\)", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split("/")[0]
                item2 = (stem.split("/(")[1]).split("))")[0]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
        # B/B=((12)/(73))
        if re.match("B/B=\(\([\d.]+\)/\([\d.]+\)\)", stem):
            inds = re.match("B/B=\(\([\d.]+\)/\([\d.]+\)\)", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split("=((")[1].split(")/(")[0]
                item2 = (stem.split(")/(")[1]).split("))")[0]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
        # ((1.2)/(73))=B/B
        if re.match("\(\([\d.]+\)/\([\d.]+\)\)=B/B", stem):
            inds = re.match("\(\([\d.]+\)/\([\d.]+\)\)=B/B", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split("((")[1].split(")/(")[0]
                item2 = (stem.split(")/(")[1]).split("))")[0]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
        # (B/B)=12/7.3
        if re.match("\(B/B\)=[\d.]+/[\d.]+", stem):
            inds = re.match("\(B/B\)=[\d.]+/[\d.]+", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split(")=")[1].split("/")[0]
                item2 = stem.split(")=")[1].split("/")[1]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
        # ((B)/(B))=12/7.3
        if re.match("\(\(B\)/\(B\)\)=[\d.]+/[\d.]+", stem):
            inds = re.match("\(\(B\)/\(B\)\)=[\d.]+/[\d.]+", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split(")=")[1].split("/")[0]
                item2 = stem.split(")=")[1].split("/")[1]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )

        # ((B)/(73))=12/B
        if re.match("\(\(B\)/\([\d.]+\)\)=[\d.]+/B", stem):
            inds = re.match("\(\(B\)/\([\d.]+\)\)=[\d.]+/B", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split(")/(")[1].split("))")[0]
                item2 = stem.split("))=")[1].split("/")[0]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
        # (B/(73))=12/B
        if re.match("\(B/\([\d.]+\)\)=[\d.]+/B", stem):
            inds = re.match("\(B/\([\d.]+\)\)=[\d.]+/B", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split("/(")[1].split("))")[0]
                item2 = stem.split("))=")[1].split("/")[0]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )

        # ((12)/B)=B/7.3
        if re.match("\(\([\d.]+\)/B\)=B/[\d.]+", stem):
            inds = re.match("\(\([\d.]+\)/B\)=B/[\d.]+", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split("((")[1].split(")/")[0]
                item2 = stem.split("=B/")[1]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )

        # ((12)/(B))=B/7.3
        if re.match("\(\([\d.]+\)/\(B\)\)=B/[\d.]+", stem):
            inds = re.match("\(\([\d.]+\)/\(B\)\)=B/[\d.]+", stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                item1 = stem.split("((")[1].split(")/")[0]
                item2 = stem.split("=B/")[1]
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )

        return None


# 处理 120kg = 120/1000t 的情况
class SQT14Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        stem = stem.replace("(B)", "B")
        # 45kg=(B/B)t  , 43cmW=((B)/(B))mW
        items = stem.split("=")
        if len(items) != 2:
            return None
        key1 = ""
        for i in range(len(items[0])):
            if items[0][i:] in util.units.keys():
                key1 = items[0][i:]
                break

        key2 = ""
        for i in range(len(items[1])):
            if items[1][i:] in util.units.keys():
                key2 = items[1][i:]
                break

        if len(key1) == 0 or len(key2) == 0:
            return None

        if re.match("[\d.]+{}=\(B/B\){}".format(key1, key2), stem):
            inds = re.match("[\d.]+{}=\(B/B\){}".format(key1, key2), stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                return True

    # 处理 120kg = 120/1000t 的情况
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        stem = stem.replace("(B)", "B")
        # 45kg=(B/B)t  , 43cmW=((B)/(B))mW

        items = stem.split("=")
        if len(items) != 2:
            return None
        key1 = ""
        for i in range(len(items[0])):
            if items[0][i:] in util.units.keys():
                key1 = items[0][i:]
                break

        key2 = ""
        for i in range(len(items[1])):
            if items[1][i:] in util.units.keys():
                key2 = items[1][i:]
                break

        if len(key1) == 0 or len(key2) == 0:
            return None

        if re.match("[\d.]+{}=\(B/B\){}".format(key1, key2), stem):
            #         print(key1,key2)
            inds = re.match("[\d.]+{}=\(B/B\){}".format(key1, key2), stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                complex_units = {
                    "分": {
                        "cats": [["半小时", "小时", "时", "秒"], ["元", "角"]],
                        "replaces": ["*60", "*1"],
                    },
                }
                if key1 == "分" and key2 in ["半小时", "小时", "时", "秒"]:
                    multi1 = 60
                    multi2 = int(util.units[key2][1:])
                elif key1 == "分" and key2 in ["元", "角"]:
                    multi1 = 1
                    multi2 = int(util.units[key2][1:])
                elif key2 == "分" and key1 in ["元", "角"]:
                    multi2 = 1
                    multi1 = int(util.units[key2][1:])
                elif key2 == "分" and key1 in ["半小时", "小时", "时", "秒"]:
                    multi2 = 60
                    multi1 = int(util.units[key2][1:])
                else:
                    multi1 = int(util.units[key1][1:])
                    multi2 = int(util.units[key2][1:])
                item1 = stem.split(key1)[0]
                item2 = str(multi2 // multi1)
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
        return None


# 处理()元()角=1.2元 的情况
class SQT15Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        # B元B角=1.2元
        num_and_point = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "."]
        items = stem.split("B")
        if len(items) != 3:
            return None
        key1 = items[1]
        if not key1 in util.units.keys():
            return None
        key2 = items[2].split("=")[0]
        if not key2 in util.units.keys():
            return None
        key3 = ""
        if len(items[2].split("=")) != 2:
            return None
        for i, k in enumerate(items[2].split("=")[1]):
            if k in num_and_point:
                continue
            else:
                key3 = items[2].split("=")[1][i:]
                break
        if not key3 in util.units.keys():
            return None
        if re.match("B{}B{}=[\d.]+{}".format(key1, key2, key3), stem):
            inds = re.match("B{}B{}=[\d.]+{}".format(key1, key2, key3), stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                return True

    # 处理()元()角=1.2元 的情况
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        # B元B角=1.2元
        num_and_point = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "."]
        items = stem.split("B")
        if len(items) != 3:
            return None
        key1 = items[1]
        if not key1 in util.units.keys():
            return None
        key2 = items[2].split("=")[0]
        if not key2 in util.units.keys():
            return None
        key3 = ""
        if len(items[2].split("=")) != 2:
            return None
        for i, k in enumerate(items[2].split("=")[1]):
            if k in num_and_point:
                continue
            else:
                key3 = items[2].split("=")[1][i:]
                break
        if not key3 in util.units.keys():
            return None
        if re.match("B{}B{}=[\d.]+{}".format(key1, key2, key3), stem):
            inds = re.match("B{}B{}=[\d.]+{}".format(key1, key2, key3), stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                multis = [0, 0, 0]
                for ki, kv in enumerate([key1, key2, key3]):
                    if kv == "分":
                        multis[ki] = 1
                        for kv_ in [key1, key2, key3]:
                            if kv_ in ["半小时", "小时", "时", "秒"]:
                                multis[ki] = 60
                    else:
                        multis[ki] = int(util.units[kv][1:])
                final_number = int(
                    float(stem.split("=")[1].replace(key3, "")) * multis[2]
                )
                item1 = str(final_number // multis[0])
                item2 = (final_number - final_number // multis[0] * multis[0]) / multis[
                    1
                ]
                if abs(int(item2) - item2) < 1e-6:
                    item2 = str(int(item2))
                else:
                    item2 = str(item2)
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
        return None


# 处理1.3元=B元B角
class SQT16Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        # 1.3元=B元B角
        num_and_point = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "."]
        items = stem.split("B")
        if len(items) != 3:
            return None
        key3 = items[2]
        if not key3 in util.units.keys():
            return None
        key2 = items[1]
        if not key2 in util.units.keys():
            return None
        key1 = ""
        for i, k in enumerate(items[0]):
            if k in num_and_point:
                continue
            else:
                key1 = items[0][i:-1]
                break
        if not key1 in util.units.keys():
            return None

        if re.match("B{}B{}=[\d.]+{}".format(key1, key2, key3), stem):
            inds = re.match("B{}B{}=[\d.]+{}".format(key1, key2, key3), stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                return True

    # 处理1.3元=B元B角
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        # 1.3元=B元B角
        num_and_point = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "."]
        items = stem.split("B")
        if len(items) != 3:
            return None
        key3 = items[2]
        if not key3 in util.units.keys():
            return None
        key2 = items[1]
        if not key2 in util.units.keys():
            return None
        key1 = ""
        for i, k in enumerate(items[0]):
            if k in num_and_point:
                continue
            else:
                key1 = items[0][i:-1]
                break
        if not key1 in util.units.keys():
            return None

        if re.match("B{}B{}=[\d.]+{}".format(key1, key2, key3), stem):
            inds = re.match("B{}B{}=[\d.]+{}".format(key1, key2, key3), stem).span()
            if inds[0] == 0 and inds[-1] == len(stem):
                multis = [0, 0, 0]
                for ki, kv in enumerate([key1, key2, key3]):
                    if kv == "分":
                        multis[ki] = 1
                        for kv_ in [key1, key2, key3]:
                            if kv_ in ["半小时", "小时", "时", "秒"]:
                                multis[ki] = 60
                    else:
                        multis[ki] = int(util.units[kv][1:])
                final_number = int(
                    float(stem.split("=")[1].replace(key3, "")) * multis[2]
                )
                item1 = str(final_number // multis[0])
                item2 = (final_number - final_number // multis[0] * multis[0]) / multis[
                    1
                ]
                if abs(int(item2) - item2) < 1e-6:
                    item2 = str(int(item2))
                else:
                    item2 = str(item2)
                answer = [item1, item2]
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )

        return None


# 处理r'^(B[元角分])(\d+[元角分])=(\d+[元角分])$'
class SQT17Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        pattern = r"^B[元角分]\d+[元角分]=\d+[元角分]$"
        if re.match(pattern, stem):
            return True
        else:
            return False

    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        match = re.match(r"^B([元角分])(\d+)([元角分])=(\d+)([元角分])$", stem)
        if match:
            s1 = match.group(1)
            s2 = match.group(2)
            s3 = match.group(3)
            s4 = match.group(4)
            s5 = match.group(5)
        std_ans = aeval.eval(
            f'({s4}{util.units[s5]}-{s2}{util.units[s3]})/{util.units[s1].replace("*", "")}'
        )
        first_flag = util.judge_answer_blur(std_ans, answer_list[0])
        if first_flag is True:
            final["flag"] = "0"
            return final
        else:
            final["flag"] = "1"
            final["reg_answers"] = answer_list
            final["std_answers"] = [str(util.find_best_round(std_ans))]
        return final


# 1000000000=类似题型
class SQT18Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if re.match("\d{4,}=", stem) and stem[-3] == "=":
            return True

    # 1000000000=类似题型
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        if re.match("\d{4,}=", stem) and stem[-3] == "=":
            answer = util.change_unit(stem)
            return util.handle_answer_return(
                answer, ori_line, pred_str, stem, answer_list
            )


# 16810333@类似题型
class SQT19Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if re.match("\d{4,}@", stem):
            return True

    # 16810333@类似题型
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        if re.match("\d{4,}@", stem):
            answer = util.change_unit_environ(stem, answer_list)
            return util.handle_answer_return(
                answer, ori_line, pred_str, stem, answer_list
            )


# 余数计算，仅支持5种情况：1.计算被除数；2.计算除数；3.计算商；4.计算余数；5.计算商+余数
class SQT20Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if stem.find("P") != -1:
            return True

    # 余数计算，仅支持5种情况：1.计算被除数；2.计算除数；3.计算商；4.计算余数；5.计算商+余数
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        if stem.find("P") != -1:
            answer = util.cal_remainder(stem)
            return util.handle_answer_return(
                answer, ori_line, pred_str, stem, answer_list
            )


# 处理三七二十一这种乘法口诀的情况，仅支持计算'三七（）'的情况
class SQT21Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if re.match("[一二三四五六七八九]{2}", stem) and len(stem) <= 4:
            return True

    # 处理三七二十一这种乘法口诀的情况，仅支持计算'三七（）'的情况
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        if re.match("[一二三四五六七八九]{2}", stem) and len(stem) <= 4:
            answer = util.cal_chi(stem)
            return util.handle_answer_return(
                answer, ori_line, pred_str, stem, answer_list
            )


# 处理未知数加减乘
class SQT22Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        stem, uncertain = util.judge_uncertain(stem)
        return uncertain

    # 处理未知数加减乘
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        stem, uncertain = util.judge_uncertain(stem)
        stem = stem[: stem.find("=") + 1] + "B"
        answer = util.cal_answer(stem_ori, stem)
        if uncertain and answer is not None:
            answer = str(answer) + uncertain
            return util.handle_answer_return(
                answer, ori_line, pred_str, stem, answer_list
            )


# 处理'=()元()角'这种情况
class SQT23Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        stem, multi_list = util.judge_multi(stem)
        return multi_list

    # 处理'=()元()角'这种情况
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        stem, multi_list = util.judge_multi(stem)
        answer = util.cal_answer(stem_ori, stem)

        if len(multi_list) > 0 and answer is not None:
            answer = util.change_answer_multiunit_list(answer, multi_list)
            return util.handle_answer_return(
                answer, ori_line, pred_str, stem, answer_list
            )


# 处理'=B*12B*100'这种情况
class SQT24Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        stem, multi_list_4B = util.judge_multi_4B(stem)
        return multi_list_4B

    # 处理'=B*12B*100'这种情况
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        stem, multi_list_4B = util.judge_multi_4B(stem)
        answer = util.cal_answer(stem_ori, stem)
        if len(multi_list_4B) > 0 and answer is not None:
            answer = util.change_answer_multiunit_list(answer, multi_list_4B)
            return util.handle_answer_return(
                answer, ori_line, pred_str, stem, answer_list
            )


class SQT25Strategy(SpecialQuestionStrategy):
    """
    处理复杂约等于表达式的策略，支持小数

    例：
    - pred_str = '554/($8$)≈70'
    - pred_str = '(955-237)/9≈$80$'
    """

    @property
    def regexp(self):
        return re.compile(
            r"^"  # 起始锚点
            r"(?=.*B)(?=.*@)"  # 必须同时包含B和@
            r"(?!.*B.*@.*B)"  # 禁止@两侧都有B
            r"[\d+B()*+/=\-\.]+"  # 左侧表达式（允许数字、小数点、B、运算符）
            r"@"  # 分隔符
            r"[\d+B()*+/=\-\.]+"  # 右侧表达式（允许数字、小数点、B、运算符）
            r"$"  # 结束锚点
        )

    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if not stem:
            return False
        return util.find_space(stem) == 1 and re.match(self.regexp, stem)

    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if not stem or not answer_list:
            final["flag"] = "4"
            return final

        std_ans, formulas = util.gusuan(stem)

        # 直接判断答案是否在标准答案列表中
        if any(answer_list[0] == result for result in std_ans):
            final["flag"] = "0"
        else:
            final["flag"] = "1"
            final["reg_answers"] = [answer_list[0]]
            final["std_answers"] = formulas if formulas else std_ans

        return final


# 答案只有两个空，且格式为'{()/()}
class SQT26Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if util.find_space(stem) == 2 and re.search("\{[BC()]+/[BC()]+\}", stem):
            return True

    # 答案只有两个空，且格式为'{()/()}
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        if util.find_space(stem) == 2 and re.search("\{[BC()]+/[BC()]+\}", stem):
            a = re.search("\{[BC()]+/[BC()]+\}", stem).span()
            stem = stem.replace(stem[a[0] : a[1]], "B")
            answer = util.cal_answer(stem_ori, stem)
            answer = util.change_float_frac_list(answer)
            return util.handle_answer_return(
                answer, ori_line, pred_str, stem, answer_list
            )


# 特别处理式子有多个等号的情况，如1+2=()+0=()
class SQT27Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        stem = util.preprocess_units(stem, stem_ori)
        if stem.find("=") != stem.rfind(
            "="
        ):  # 特别处理式子有多个等号的情况，如1+2=（）+0=（）
            if (
                stem.find(">") != -1 or stem.find("<") != -1
            ):  # 如果有多个等号并且还有<,>号，暂不支持处理
                return None
            items = stem.split("=")
            answer_ = None
            for item in items:
                n = util.find_space(item)
                if n > 2:
                    return None  # 如果每个计算单元内有多个空，不支持。如：（）+（）=2+（）=8
                if n == 0:
                    answer_ = aeval.eval(item)
            if answer_ is None:
                return None  # 没有找到可计算答案的单元
            return True

    # 特别处理式子有多个等号的情况，如1+2=（）+0=（）
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        stem = util.preprocess_units(stem, stem_ori)
        if stem.find("=") != stem.rfind(
            "="
        ):  # 特别处理式子有多个等号的情况，如1+2=（）+0=（）
            if (
                stem.find(">") != -1 or stem.find("<") != -1
            ):  # 如果有多个等号并且还有<,>号，暂不支持处理
                return None
            items = stem.split("=")
            answer_ = None
            for item in items:
                n = util.find_space(item)
                if n > 2:
                    return None  # 如果每个计算单元内有多个空，不支持。如：（）+（）=2+（）=8
                if n == 0:
                    answer_ = aeval.eval(item)
            if answer_ is None:
                return None  # 没有找到可计算答案的单元
            answers = []
            num = 0
            for item in items:
                n = util.find_space(item)
                if n == 1:
                    stem = item + "={}".format(answer_)
                    final_result = SpecialQuestionHandler().handle_special_question(
                        ori_line, final, pred_str, stem, [answer_list[num]]
                    )
                    if final_result["flag"] == "0":
                        answers.append(answer_list[num])
                    else:
                        if (
                            final_result["std_answers"] != "None"
                            or len(final_result["flag"]) != 0
                        ):
                            answers.append(final_result["std_answers"][0])

                    num += 1
                if n == 2:
                    stem = str(answer_) + "={}".format(item)
                    final_result = SpecialQuestionHandler().handle_special_question(
                        ori_line,
                        final,
                        pred_str,
                        stem,
                        [answer_list[num], answer_list[num + 1]],
                    )
                    if final_result["flag"] == "0":
                        answers.extend([answer_list[num], answer_list[num + 1]])
                    else:
                        if (
                            final_result["std_answers"] != "None"
                            or len(final_result["flag"]) != 0
                        ):
                            answers.extend(final_result["std_answers"])
                    num += 2
            return util.handle_answer_return(
                answers, ori_line, pred_str, stem, answer_list
            )


# 最基础的加减乘除
class SQT28Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        stem = util.preprocess_units(stem, stem_ori)
        answers_needed = 0
        for space in util.spaces:
            if stem.find(space) != -1:
                answers_needed += 1
        if answers_needed > 1:  # 多个答案空，暂不支持
            return None
        if stem.find("=") != -1:  # 有=号存在的情况
            if stem.find("=") != stem.rfind("="):
                return None  # 式子中有两个=，暂时不支持
            else:
                return True
        else:
            return False

    # 最基础的加减乘除
    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        if (stem and answer_list) is None:
            final["flag"] = "4"
            return final
        answer = util.cal_answer(stem_ori, stem)
        return util.handle_answer_return(answer, ori_line, pred_str, stem, answer_list)


class SQT29Strategy(SpecialQuestionStrategy):
    def check(self, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        stem = util.preprocess_units(stem, stem_ori)
        answers_needed = 0
        for space in util.spaces:
            if stem.find(space) != -1:
                space_ = space
                answers_needed += 1
        if answers_needed > 1:  # 多个答案空，暂不支持
            return None
        if "=" not in stem:
            if answers_needed == 0:  # 没有答题区域
                return None
            num_com = (
                0  # num_com = 1,情况1；num_com = 0情况2;num_com >1,多个符号，不支持；
            )
            for compare in util.compares:
                if stem.find(compare) != stem.rfind(compare):
                    return None  # 暂不支持式子里面有两个>,<符号
                if stem.find(compare) != -1:
                    num_com += 1
            if num_com > 1:
                return None  # 暂不支持式子里面有两个>,<符号
            else:
                return True

    def handle(self, ori_line, final, pred_str, stem, answer_list):
        stem, answer_list, stem_ori = util.preprocess_stem2(stem, answer_list)
        stem = util.preprocess_units(stem, stem_ori)
        answers_needed = 0
        for space in util.spaces:
            if stem.find(space) != -1:
                space_ = space
                answers_needed += 1
        if answers_needed > 1:  # 多个答案空，暂不支持
            return None
        if "=" not in stem:
            if answers_needed == 0:  # 没有答题区域
                return None
            num_com = (
                0  # num_com = 1,情况1；num_com = 0情况2;num_com >1,多个符号，不支持；
            )
            for compare in util.compares:
                if stem.find(compare) != stem.rfind(compare):
                    return None  # 暂不支持式子里面有两个>,<符号
                if stem.find(compare) != -1:
                    num_com += 1
            if num_com > 1:
                return None  # 暂不支持式子里面有两个>,<符号
            elif (
                num_com == 1
            ):  # 情况1，包含>,<号;答案可能为数字或者+-*/，或者填写最大最小数字使得不等式成立
                answer = util.cal_non_equation_type1(stem, space_)
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )
            else:  # 情况2，式子中不包含>,<,=号,答案默认只为>,<,=
                answer = util.cal_non_equation_type2(stem, space_)
                return util.handle_answer_return(
                    answer, ori_line, pred_str, stem, answer_list
                )


# 策略模式
class SpecialQuestionHandler:
    strategies = {
        SQT.sqt_1: SQT1Strategy(),  # F{12}{18}=2:3
        SQT.sqt_2: SQT2Strategy(),  # 3+5*(3-1)=B3, 100/4=B5左边任意的复杂表达式，右边B数字
        SQT.sqt_3: SQT3Strategy(),  # 1.2元+1.3元=$2.5元$ 题型
        SQT.sqt_4: SQT4Strategy(),  # 4702300092≈$15亿$  题型
        SQT.sqt_5: SQT5Strategy(),  # 4702300092=(B)亿  题型
        SQT.sqt_6: SQT6Strategy(),  # 1$+$2$-$2=1题型
        SQT.sqt_7: SQT7Strategy(),  # 处理1个数的约等于的情况
        SQT.sqt_8: SQT8Strategy(),  # 枚举估算
        SQT.sqt_9: SQT9Strategy(),  # 比大小题型
        SQT.sqt_10: SQT10Strategy(),  # 小数转分数，只支持'1.25='格式
        SQT.sqt_11: SQT11Strategy(),  # 小数转分数，只支持'0.05=F{($1$)}{($10$)}'格式
        SQT.sqt_12: SQT12Strategy(),  # 分数转小数，只支持'1/2=$0.2$'格式  '1/2=$0.2$'
        SQT.sqt_13: SQT13Strategy(),  # 处理 12/71=(($12$)/($71$))的情况
        SQT.sqt_14: SQT14Strategy(),  # 处理 120kg = 120/1000t 的情况
        SQT.sqt_15: SQT15Strategy(),  # 处理()元()角=1.2元 的情况
        SQT.sqt_16: SQT16Strategy(),  # 处理1.3元=处理()元()角 的情况
        SQT.sqt_17: SQT17Strategy(),  # 处理r'^(B[元角分])(\d+[元角分])=(\d+[元角分])$' 的情况
        SQT.sqt_18: SQT18Strategy(),  # 1000000000=类似题型
        SQT.sqt_19: SQT19Strategy(),  # 16810333@类似题型
        SQT.sqt_20: SQT20Strategy(),  # 余数计算，仅支持5种情况：1.计算被除数；2.计算除数；3.计算商；4.计算余数；5.计算商+余数
        SQT.sqt_21: SQT21Strategy(),  # 处理三七二十一这种乘法口诀的情况，仅支持计算'三七（）'的情况
        SQT.sqt_22: SQT22Strategy(),  # 处理未知数加减乘
        SQT.sqt_23: SQT23Strategy(),  # 处理'=()元()角'这种情况
        SQT.sqt_24: SQT24Strategy(),  # 处理'=B*12B*100'这种情况
        SQT.sqt_25: SQT25Strategy(),  # 处理复杂约等于表达式的策略，支持小数
        # basic terms
        SQT.sqt_26: SQT26Strategy(),  # 答案只有两个空，且格式为'{()/()}
        SQT.sqt_27: SQT27Strategy(),  # 特别处理式子有多个等号的情况，如1+2=()+0=()
        SQT.sqt_28: SQT28Strategy(),  # 最基础的加减乘除
        SQT.sqt_29: SQT29Strategy(),  # 答案可能为数字或者+-*/,或者填写最大最小数字使得不等式成立或者式子中不包含>,<,=号,答案默认只为>,<,=
    }

    @classmethod
    def handle_special_question(cls, ori_line, final, pred_str, stem, answer_list):
        for sqt_type, strategy in cls.strategies.items():
            # 在这里编写处理逻辑
            try:
                if strategy.check(pred_str, stem, answer_list):
                    return strategy.handle(ori_line, final, pred_str, stem, answer_list)
            except Exception as e:
                print(e)
                print(sqt_type)


if __name__ == "__main__":
    ori_line = ""
    pred_str = ori_line
    stem, answer_list = util.replace_dollar(pred_str)
    final = {"flag": "3", "reg_answers": [], "std_answers": []}
    sqh = SpecialQuestionHandler()
    final = sqh.handle_special_question(ori_line, final, pred_str, stem, answer_list)
    print(final)
