# -*- coding: UTF-8 -*-
# @Project      ：correction_runtime 
# @IDE          ：PyCharm 
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/8/3 9:55 
# @Description  : service_of_base.py
from ..context import Context
from ..logger import LoggerFactory
from ..util.util_of_image import ImageUtil
from ..service.service_of_redis import RedisManager
from ..missions.mission_consumer import MissionConsumer

log = LoggerFactory.get_logger('BaseService')

class BaseService:
    def __init__(self):
        self.redis_service = None
        self.mission_queue_redis = None
        self.mission_mode = None
        self._counter = 0
        self._use_pil_bytes = True
        self.use_ai_lab = False
        self.ai_lab_redis = None

    def init_redis(self):
        self.redis_service = RedisManager.get_mission_data_redis(self.mission_mode)
        self.mission_queue_redis = RedisManager.get_mission_queue_redis()
        val = self.mission_queue_redis.get_val('CONST:USE_PIL_BYTES')
        self._use_pil_bytes = '0' == val
    def __use_pil_bytes(self):
        self._counter += 1
        if self._counter >= 50:
            self._counter = 0
            val = self.mission_queue_redis.get_val('CONST:USE_PIL_BYTES')
            log.debug(f'CONST:PIL_BYTES value: {val}')
            self._use_pil_bytes = '0' == val
        return self._use_pil_bytes

    def set_use_ai_lab(self, use_ai_lab=False):
        self.use_ai_lab = use_ai_lab

    def set_image(self, image, is_ai_lab=False, mission_id=None, img_fmt='.jpg'):
        if image is None:
            return None, None
        if self.__use_pil_bytes():
            img_type = 'PIL_BYTES'
            img_data = ImageUtil.numpy_2_pil_bytes(image, img_fmt)
        else:
            img_type = 'NUMPY_BYTES'
            img_data = ImageUtil.numpy_2_bytes(image, img_fmt)

        img_key = self.redis_service.set_val(img_data) if not is_ai_lab else self.ai_lab_redis.set_val(img_data)
        Context.add_img_key(mission_id, img_key)
        return img_key, img_type

    def get_image(self, data_json, is_ai_lab=False):
        if data_json is None:
            return None
        img_type = data_json.get('img_type', None)
        img_key = data_json.get('img_key', None)
        if img_key is None:
            return None
        img_data = self.redis_service.get_bytes(img_key) if not is_ai_lab else self.ai_lab_redis.get_bytes(img_key)
        if img_data is None:
            log.info(f"{'AI_LAB' if is_ai_lab else '拍照批改'} 任务获取图片为空")
        if img_type == 'PIL_BYTES':
            image = ImageUtil.pil_bytes_2_numpy(img_data)
        else:
            image = ImageUtil.bytes_2_numpy(img_data)
        return image

class BaseModelService(BaseService):
    def __init__(self, timeout=16):
        super().__init__()
        self.timeout = timeout
        self.consumer = None
        self.stop_time = '0'
    def set_stop_time(self, stop_time='0'):
        self.stop_time = stop_time
    def start(self, port, no_torch=False):
        self.init_redis()
        self.consumer = MissionConsumer(self.do_post, self.mission_mode, port, no_torch, self.timeout)

        if self.use_ai_lab and Context.is_product():
            self.ai_lab_redis = RedisManager.get_ai_lab_data_redis()
            self.consumer.init_redis(self.mission_queue_redis, RedisManager.get_ai_lab_queue_redis())
        else:
            self.consumer.init_redis(self.mission_queue_redis)
        self.consumer.start(self.stop_time)

    def do_post(self, data_json, is_ai_lab=False):
        return None