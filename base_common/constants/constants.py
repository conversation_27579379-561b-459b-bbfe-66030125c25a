# -*- coding: UTF-8 -*-
# @Project      ：correction_service
# @IDE          ：PyCharm
# <AUTHOR> yanh<PERSON>
# @Date         ：2024/6/5 10:09
import os
import socket

class Constants:
    MODEL_WEIGHT_PATH = "/mnt/model_weights/product"
    SEARCH_WEIGHT_YAML = f"{MODEL_WEIGHT_PATH}/search_weight.yaml"
    WORK_BASE_PATH = os.getenv("PYTHONPATH")

    # 影响搜页的阈值
    CHOICE_CHS = {'A', 'B', 'C', 'D'}
    JUDGMENT_CHS = {'√', '×', 'T', 'F', 'Y', 'N'}
    NUMBER_CHS = {chr(s) for s in range(48, 58)}.union({
        '(', ')', '.', '-', '①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨', '⑩', '⑪', '⑫', '⑬', '⑭', '⑮', '⑯', '⑰'
    })
    OPERATOR_CHS = {'+', '-', '×', '÷', '<', '>', '=', '(', ')'}
    NUMBOROP_CHS = NUMBER_CHS.union(OPERATOR_CHS)
    NORMAL_CHS = {chr(s) for s in range(48, 58)}
    ALPHABET_CHS = {chr(s) for s in range(65, 91)}.union({chr(s) for s in range(97, 123)})
    DECODE_TABLE = {chr(ord('z') - i): chr(ord('a') + i) for i in range(26)}

    BATCH_SIZE_FORM = 8
    CORRECTION_MISSION_NUMBER = 8
    QUICK_CORRECTION_MISSION_NUMBER = 8
    WORKING_TASK_COUNT_KEY = "CORRECTION_WORKING_COUNT"

    BUCKETS = [[32, 64],
               [64, 32], [64, 64], [64, 96],
               [96, 32], [96, 64], [96, 96], [96, 128], [96, 192],
               [128, 32], [128, 64], [128, 96], [128, 128], [128, 192],
               [192, 32], [192, 64], [192, 96], [192, 128], [192, 192], [192, 256],
               [256, 32], [256, 64], [256, 96], [256, 128], [256, 192], [256, 256],
               [320, 32], [320, 64], [320, 96], [320, 128], [320, 192], [320, 256],
               [480, 32], [480, 64], [480, 96], [480, 128], [480, 192], [480, 256],
               [640, 64], [640, 96], [640, 128], [640, 192], [640, 256],
               [800, 64], [800, 96], [800, 128], [800, 192], [800, 256]]

    MESSAGE_URL = 'https://correction-monitor.yuntim.com/api/message.do'
    CHART_GPT_URL = "http://47.251.16.45/send/text.do"

    MNT_PATH = '/mnt'
    TEMP_PATH = f'{WORK_BASE_PATH}/tmp'
    LOGGER_PATH = f'{WORK_BASE_PATH}/logs'
    VIS_PATH = f'{WORK_BASE_PATH}/vis'

    VIS_RESULTS_PATH = f'{VIS_PATH}/vis_results'
    VIS_QUICK_RESULTS_PATH = f'{VIS_PATH}/vis_quick_results'
    QUICK_CORRECTION_PATH = f'{VIS_PATH}/quick_correction'

    VIS_VERTICAL_DET_PATH = f'{VIS_PATH}/vis_shushi_det'
    VIS_VERTICAL_REC_PATH = f'{VIS_PATH}/vis_shushi_rec'
    VIS_QUICK_DET_PATH = f'{VIS_PATH}/vis_quick_det'
    VIS_QUICK_REC_PATH = f'{VIS_PATH}/vis_quick_rec'

    FIX_IP_LIST = ['*************', '*************', '*************', '************', '**************', '************', '************']
    SAVE_VIS = False
    IS_PRODUCT = True
    try:
        # 获取主机名
        hostname = socket.gethostname()
        # 通过主机名获取IP地址
        LOCAL_IP = socket.gethostbyname(hostname)
    except:
        LOCAL_IP = 'Unknown'
    EMPTY_IMG = '/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAAKABQDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAr/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/AL+AAAAf/9k='

    @classmethod
    def __check_dir(cls, dir_path):
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
    @classmethod
    def setup(cls, is_product=False):
        cls.IS_PRODUCT = is_product
        if not is_product:
            cls.SAVE_VIS = True
            cls.MODEL_WEIGHT_PATH = "/mnt/model_weights/prepub"
            cls.SEARCH_WEIGHT_YAML = f"{cls.MODEL_WEIGHT_PATH}/search_weight.yaml"

        # Load HANZI from file
        with open(f'{cls.MODEL_WEIGHT_PATH}/answer_det_service/char.txt', 'r', encoding='utf-8') as f:
            content = f.readlines()

        cls.HANZI_CHS = set([s.strip() for s in content]).difference(cls.NUMBER_CHS, cls.JUDGMENT_CHS, cls.CHOICE_CHS)

        cls.__check_dir(cls.TEMP_PATH)
        cls.__check_dir(cls.VIS_PATH)
        cls.__check_dir(cls.VIS_RESULTS_PATH)
        cls.__check_dir(cls.VIS_QUICK_RESULTS_PATH)
        cls.__check_dir(cls.VIS_VERTICAL_DET_PATH)
        cls.__check_dir(cls.VIS_VERTICAL_REC_PATH)
        cls.__check_dir(cls.VIS_QUICK_DET_PATH)
        cls.__check_dir(cls.VIS_QUICK_REC_PATH)

class TopicMode:
    CHOICE = 'choise'
    JUDGMENT = 'judgment'
    NUMBER = 'number'
    OPERATOR = 'operator'
    NUMBOROP = 'numborop'
    NORMAL = 'normal'
    ENGLISH = 'english'
    HANZI = 'hanzi'
    LATEX = 'latex'
    TEXT = 'text'
    LIANXIAN = 'lianxian'
    ZUOTU = 'zuotu'
    STANDING_FORM_CHECK = 'standing_form_check'

class TopicType:
    FILL_BLANK = 8  # 填空题
    CONNECT_LINE = 9  # 连线题
    SINGLE_CHOICE = 13  # 单选题
    MULTI_CHOICE = 14  # 多选题
    JUDGE_YES_NO = 17  # 判断题
    OFF_COMPUTE = 18  # 脱式计算
    WORD_PROBLEM = 19  # 应用题
    DRAW_IMG = 21  # 作图题
    ORAL_CALC = 23  # 口算题
    INTELLIGENT_ORAL_CALC = 25  # 智能口算题
    VERTICAL_CALC = 27  # 竖式计算
    VERTICAL_CALC_WITH_CHECK = 28  # 竖式计算带验算

TopicTypeStr = {
    TopicType.FILL_BLANK: "填空题",
    TopicType.CONNECT_LINE: "连线题",
    TopicType.SINGLE_CHOICE: "单选题",
    TopicType.MULTI_CHOICE: "多选题",
    TopicType.JUDGE_YES_NO: "判断题",
    TopicType.OFF_COMPUTE: "脱式计算",
    TopicType.WORD_PROBLEM: "应用题",
    TopicType.DRAW_IMG: "作图题",
    TopicType.ORAL_CALC: "口算题",
    TopicType.INTELLIGENT_ORAL_CALC: "智能口算题",
    TopicType.VERTICAL_CALC: "竖式计算",
    TopicType.VERTICAL_CALC_WITH_CHECK: "竖式计算带验算"
}